# Laravel ERP Technology Stack Documentation

## Table of Contents
- [Technology Stack Overview](#technology-stack-overview)
- [Complete Installation & Setup Guide](#complete-installation--setup-guide)
- [Technology Justification Matrix](#technology-justification-matrix)
- [Development Workflow Reference](#development-workflow-reference)
- [Project Structure & Conventions](#project-structure--conventions)

**Estimated Total Setup Time:** 4-6 hours for complete stack  
**Prerequisites:** PHP 8.2+, Node.js 18+, Composer, MySQL 8.0+  
**Target Laravel Version:** 11.x LTS

---

## Technology Stack Overview

| Technology Name | Exact Version | Role in ERP System | Installation Status | Dependencies |
|----------------|---------------|-------------------|-------------------|--------------|
| Laravel | ^11.0 | Core PHP framework and API backend | [ ] Not Installed | PHP 8.2+, Composer |
| Laravel Breeze | ^2.0 | Authentication scaffolding system | [ ] Not Installed | Laravel 11+ |
| Livewire | ^3.4 | Dynamic frontend without JavaScript | [ ] Not Installed | Laravel 11+ |
| Alpine.js | ^3.14 | Minimal JavaScript interactions | [ ] Not Installed | Node.js 18+ |
| Tailwind CSS | ^3.4 | Utility-first CSS framework | [ ] Not Installed | Node.js 18+ |
| Filament | ^3.2 | Admin panel and resource management | [ ] Not Installed | Livewire 3+ |
| Spatie Permissions | ^6.4 | Role-based access control | [ ] Not Installed | Laravel 11+ |
| MySQL | 8.0+ | Primary database system | [ ] Not Installed | None |
| Laravel Sail | ^1.29 | Docker development environment | [ ] Not Installed | Docker Desktop |
| Laravel Debugbar | ^3.13 | Development debugging tools | [ ] Not Installed | Laravel 11+ |

---

## Complete Installation & Setup Guide

### Step 1: Laravel Project Creation (15 minutes)

```bash
# Create new Laravel project
composer create-project laravel/laravel lial_erp_v1
cd lial_erp_v1

# Verify installation
php artisan --version
```

**Expected Output:** `Laravel Framework 11.x.x`

### Step 2: Database Setup and Configuration (10 minutes)

```bash
# Create database
mysql -u root -p -e "CREATE DATABASE lial_erp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

**Update .env file:**
```env
# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=lial_erp
DB_USERNAME=root
DB_PASSWORD=your_password_here

# Application Configuration
APP_NAME="Lial ERP System"
APP_ENV=local
APP_DEBUG=true
APP_TIMEZONE=UTC
```

**Verification:**
```bash
php artisan migrate:status
```

### Step 3: Authentication Scaffolding - Laravel Breeze (20 minutes)

```bash
# Install Laravel Breeze
composer require laravel/breeze --dev

# Install Breeze with Livewire stack
php artisan breeze:install livewire

# Install dependencies and build assets
npm install && npm run build

# Run migrations
php artisan migrate
```

**Verification:**
```bash
php artisan serve
# Visit http://localhost:8000/login - should see login page
```

### Step 4: Frontend Setup - Tailwind CSS + Alpine.js (15 minutes)

Breeze automatically installs Tailwind CSS and Alpine.js. Verify installation:

**Check package.json:**
```json
{
  "devDependencies": {
    "@tailwindcss/forms": "^0.5.7",
    "alpinejs": "^3.14.0",
    "tailwindcss": "^3.4.0"
  }
}
```

**Verify Tailwind config exists:**
```bash
ls tailwind.config.js
```

### Step 5: Livewire Installation and Configuration (10 minutes)

Livewire is included with Breeze, but let's verify and configure:

```bash
# Publish Livewire config (optional)
php artisan livewire:publish --config

# Create test component
php artisan livewire:make Counter
```

**Verification:**
```bash
# Check if Livewire is working
php artisan livewire:list
```

### Step 6: Admin Panel Setup - Filament (30 minutes)

```bash
# Install Filament
composer require filament/filament:"^3.2"

# Install admin panel
php artisan filament:install --panels

# Create admin user
php artisan make:filament-user
```

**Follow prompts to create admin user with:**
- Name: Admin User
- Email: <EMAIL>
- Password: (secure password)

**Verification:**
```bash
php artisan serve
# Visit http://localhost:8000/admin - should see Filament login
```

### Step 7: Permission System - Spatie Permissions (25 minutes)

```bash
# Install Spatie Laravel Permission
composer require spatie/laravel-permission

# Publish and run migrations
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan migrate

# Install Filament Shield for UI integration
composer require bezhansalleh/filament-shield
php artisan vendor:publish --tag=filament-shield-config
php artisan shield:install --fresh
```

**Update User model (app/Models/User.php):**
```php
<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles;
    
    // ... rest of your User model
}
```

### Step 8: Development Tools Installation (15 minutes)

```bash
# Install debugging tools
composer require barryvdh/laravel-debugbar --dev
composer require barryvdh/laravel-ide-helper --dev

# Install code quality tools
composer require laravel/pint --dev
composer require nunomaduro/larastan --dev

# Install Laravel Sail for Docker development
composer require laravel/sail --dev
php artisan sail:install
```

**Common Error Solutions:**

1. **"Class 'Livewire\Component' not found"**
   ```bash
   composer dump-autoload
   php artisan config:clear
   ```

2. **"npm run build" fails**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

3. **Filament admin panel 404**
   ```bash
   php artisan route:clear
   php artisan config:clear
   ```

---

## Technology Justification Matrix

### Livewire + Alpine.js

**Beginner-Friendly Factors:**
- Write dynamic interfaces using only PHP syntax
- No need to learn React/Vue.js or complex JavaScript frameworks
- Real-time updates without AJAX calls or API endpoints
- Built-in form validation with Laravel's validation rules
- Automatic CSRF protection and security features

**Laravel Integration Level:** Native/First-party (5/5)
- Created by Laravel team member Caleb Porzio
- Follows Laravel conventions and patterns
- Seamless integration with Eloquent models and validation

**ERP-Specific Benefits:**
1. **Real-time Data Tables:**
   ```php
   // app/Livewire/UserTable.php
   class UserTable extends Component
   {
       public $search = '';
       
       public function render()
       {
           return view('livewire.user-table', [
               'users' => User::where('name', 'like', '%'.$this->search.'%')->get()
           ]);
       }
   }
   ```

2. **Dynamic Forms with Validation:**
   ```php
   // Real-time validation without page refresh
   public function updated($propertyName)
   {
       $this->validateOnly($propertyName, [
           'email' => 'required|email|unique:users',
           'name' => 'required|min:3'
       ]);
   }
   ```

**Known Limitations:**
- Not suitable for complex SPAs or mobile apps
- Requires stable internet connection for real-time features
- Can become slow with very large datasets (>1000 records per page)

**Workaround:** Use pagination and lazy loading for large datasets

**Learning Curve Estimate:** 2-3 days for basic CRUD, 1-2 weeks for advanced features

**Alternative Options:**
- **Inertia.js + Vue:** More complex but better for SPAs
- **Traditional Blade + jQuery:** Simpler but less dynamic

### Tailwind CSS

**Beginner-Friendly Factors:**
- Utility classes are self-documenting (e.g., `bg-blue-500`, `text-center`)
- No need to write custom CSS for most use cases
- Excellent documentation with visual examples
- Built-in responsive design utilities
- Purges unused CSS automatically in production

**Laravel Integration Level:** First-party (5/5)
- Default choice for Laravel Breeze and Jetstream
- Optimized build process with Laravel Mix/Vite
- Works seamlessly with Livewire components

**ERP-Specific Benefits:**
1. **Admin Dashboard Layouts:**
   ```html
   <!-- Professional dashboard grid -->
   <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
       <div class="bg-white p-6 rounded-lg shadow">
           <h3 class="text-lg font-semibold text-gray-900">Total Users</h3>
           <p class="text-3xl font-bold text-blue-600">{{ $userCount }}</p>
       </div>
   </div>
   ```

2. **Responsive Data Tables:**
   ```html
   <!-- Mobile-friendly table -->
   <div class="overflow-x-auto">
       <table class="min-w-full divide-y divide-gray-200">
           <thead class="bg-gray-50">
               <tr>
                   <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                       Name
                   </th>
               </tr>
           </thead>
       </table>
   </div>
   ```

**Known Limitations:**
- Large CSS file in development (solved by purging in production)
- Learning curve for developers used to traditional CSS
- Can lead to very long class strings in complex layouts

**Workaround:** Use Tailwind's `@apply` directive for frequently used combinations

**Learning Curve Estimate:** 1-2 days for basics, 1 week for advanced layouts

**Alternative Options:**
- **Bootstrap 5:** More traditional, component-based approach
- **AdminLTE:** Free admin template with pre-built components

### MySQL with Laravel Eloquent

**Beginner-Friendly Factors:**
- Laravel's default database with zero configuration
- Eloquent ORM provides intuitive syntax for database operations
- Built-in migration system for version control of database schema
- Excellent performance for typical ERP workloads
- Rich ecosystem of GUI tools (phpMyAdmin, MySQL Workbench)

**Laravel Integration Level:** Native (5/5)
- Default database driver in Laravel
- Optimized query builder and ORM
- Built-in connection pooling and query logging

**ERP-Specific Benefits:**
1. **Complex Relationships:**
   ```php
   // app/Models/Invoice.php
   class Invoice extends Model
   {
       public function client()
       {
           return $this->belongsTo(Client::class);
       }

       public function items()
       {
           return $this->hasMany(InvoiceItem::class);
       }
   }
   ```

2. **JSON Columns for Flexible Data:**
   ```php
   // Store dynamic form data
   $invoice->metadata = [
       'custom_fields' => $request->custom_fields,
       'payment_terms' => $request->payment_terms
   ];
   ```

**Known Limitations:**
- Not ideal for very large datasets (>10M records) without optimization
- JSON queries can be slower than normalized tables
- Requires careful indexing for complex reporting queries

**Workaround:** Use database indexing and query optimization for large datasets

**Learning Curve Estimate:** 1 day for basic operations, 1 week for advanced queries

**Alternative Options:**
- **PostgreSQL:** Better for complex queries and JSON operations
- **SQLite:** Simpler for development but not suitable for production

### Filament Admin Panel

**Beginner-Friendly Factors:**
- Built on Livewire (consistent with frontend choice)
- Automatic CRUD generation from Eloquent models
- Rich form builder with validation
- Built-in user management and authentication
- Extensive plugin ecosystem

**Laravel Integration Level:** Third-party/Excellent (5/5)
- Built specifically for Laravel and Livewire
- Follows Laravel conventions and best practices
- Seamless integration with Eloquent relationships

**ERP-Specific Benefits:**
1. **Resource Management:**
   ```php
   // app/Filament/Resources/ClientResource.php
   class ClientResource extends Resource
   {
       protected static ?string $model = Client::class;

       public static function form(Form $form): Form
       {
           return $form->schema([
               TextInput::make('name')->required(),
               TextInput::make('email')->email()->required(),
               Select::make('status')
                   ->options([
                       'active' => 'Active',
                       'inactive' => 'Inactive'
                   ])
           ]);
       }
   }
   ```

2. **Dashboard Widgets:**
   ```php
   // Real-time statistics widgets
   protected function getHeaderWidgets(): array
   {
       return [
           StatsOverviewWidget::class,
           RevenueChartWidget::class,
       ];
   }
   ```

**Known Limitations:**
- Learning curve for advanced customization
- Can be overkill for very simple admin needs
- Requires understanding of Livewire concepts

**Workaround:** Start with basic resources and gradually add customization

**Learning Curve Estimate:** 2-3 days for basic resources, 2 weeks for advanced customization

**Alternative Options:**
- **Laravel Nova:** Official Laravel admin panel (paid)
- **Voyager:** Free alternative with different approach

### Laravel Breeze Authentication

**Beginner-Friendly Factors:**
- Minimal, clean authentication scaffolding
- Includes login, registration, password reset, and email verification
- Uses standard Laravel features (no complex dependencies)
- Easy to customize and extend
- Includes Tailwind CSS styling out of the box

**Laravel Integration Level:** First-party (5/5)
- Official Laravel package maintained by Laravel team
- Uses Laravel's built-in authentication features
- Perfect integration with Laravel's session management

**ERP-Specific Benefits:**
1. **Role-Based Access:**
   ```php
   // Middleware for protecting routes
   Route::middleware(['auth', 'role:admin'])->group(function () {
       Route::get('/admin/users', [UserController::class, 'index']);
   });
   ```

2. **Multi-Factor Authentication Ready:**
   ```php
   // Easy to extend with 2FA packages
   composer require pragmarx/google2fa-laravel
   ```

**Known Limitations:**
- Basic features only (no advanced user management)
- No built-in role/permission system
- Requires additional packages for enterprise features

**Workaround:** Combine with Spatie Permissions for role-based access

**Learning Curve Estimate:** 1 day for basic usage, 3-4 days for customization

**Alternative Options:**
- **Laravel Jetstream:** More features but more complex
- **Laravel Fortify:** Backend-only authentication

### Spatie Laravel Permission

**Beginner-Friendly Factors:**
- Simple, intuitive API for roles and permissions
- Excellent documentation with clear examples
- Caching system for performance
- Works seamlessly with Laravel's authorization system
- Blade directives for easy template integration

**Laravel Integration Level:** Third-party/Excellent (5/5)
- Created by Spatie (trusted Laravel community member)
- Follows Laravel conventions
- Integrates with Laravel's Gate and Policy system

**ERP-Specific Benefits:**
1. **Granular Permissions:**
   ```php
   // Define specific ERP permissions
   Permission::create(['name' => 'view invoices']);
   Permission::create(['name' => 'create invoices']);
   Permission::create(['name' => 'edit invoices']);
   Permission::create(['name' => 'delete invoices']);

   // Assign to roles
   $role = Role::create(['name' => 'accountant']);
   $role->givePermissionTo(['view invoices', 'create invoices']);
   ```

2. **Blade Template Integration:**
   ```blade
   @can('edit invoices')
       <a href="{{ route('invoices.edit', $invoice) }}" class="btn btn-primary">
           Edit Invoice
       </a>
   @endcan
   ```

**Known Limitations:**
- Can become complex with many roles and permissions
- Requires careful planning of permission structure
- Performance impact with very large numbers of permissions

**Workaround:** Use permission groups and caching for large permission sets

**Learning Curve Estimate:** 2-3 days for basic setup, 1 week for complex permission structures

**Alternative Options:**
- **Laravel Bouncer:** Different approach to permissions
- **Custom implementation:** More work but full control

---

## Development Workflow Reference

### Daily Development Commands

**Server Management:**
```bash
# Start development server
php artisan serve                    # Use daily - starts local server

# Start with specific port
php artisan serve --port=8080       # Use when port 8000 is busy

# Start Laravel Sail (Docker)
./vendor/bin/sail up                 # Use daily if using Docker setup
./vendor/bin/sail up -d              # Run in background
```

**Database Operations:**
```bash
# Run pending migrations
php artisan migrate                  # Use daily - applies new database changes

# Rollback last migration
php artisan migrate:rollback         # Use when fixing migration errors

# Fresh migration with seeding
php artisan migrate:fresh --seed     # Use weekly - resets database with test data

# Check migration status
php artisan migrate:status           # Use for debugging migration issues
```

**Cache Management:**
```bash
# Clear all caches
php artisan optimize:clear           # Use daily when debugging issues

# Clear specific caches
php artisan config:clear             # Use after changing .env or config files
php artisan route:clear              # Use after changing routes
php artisan view:clear               # Use after changing Blade templates
```

### Code Generation Commands

**Livewire Components:**
```bash
# Create Livewire component
php artisan livewire:make UserForm   # Use for creating interactive forms

# Create Livewire component with inline view
php artisan livewire:make Counter --inline  # Use for simple components

# List all Livewire components
php artisan livewire:list            # Use for component overview
```

**Filament Resources:**
```bash
# Create Filament resource
php artisan make:filament-resource Client  # Use for admin CRUD interfaces

# Create resource with pages
php artisan make:filament-resource Client --generate  # Auto-generates forms and tables

# Create custom Filament page
php artisan make:filament-page Dashboard  # Use for custom admin pages
```

**Laravel Generators:**
```bash
# Create model with migration and factory
php artisan make:model Invoice -mf   # Use for new database entities

# Create controller with resource methods
php artisan make:controller InvoiceController --resource  # Use for API endpoints

# Create form request for validation
php artisan make:request StoreInvoiceRequest  # Use for complex validation rules
```

### Debugging & Testing Commands

**Debugging Tools:**
```bash
# Enable debug mode
php artisan debugbar:clear           # Use when debugbar shows old data

# View application logs
tail -f storage/logs/laravel.log     # Use for real-time error monitoring

# Tinker (Laravel REPL)
php artisan tinker                   # Use for testing code snippets
```

**Testing:**
```bash
# Run all tests
php artisan test                     # Use before committing code

# Run specific test file
php artisan test tests/Feature/InvoiceTest.php  # Use for focused testing

# Run tests with coverage
php artisan test --coverage         # Use weekly for code quality checks
```

### Code Quality Commands

**Laravel Pint (Code Formatting):**
```bash
# Format all PHP files
./vendor/bin/pint                    # Use before committing code

# Check formatting without fixing
./vendor/bin/pint --test             # Use in CI/CD pipeline

# Format specific directory
./vendor/bin/pint app/Models         # Use for targeted formatting
```

**Static Analysis:**
```bash
# Run Larastan analysis
./vendor/bin/phpstan analyse         # Use weekly for code quality

# Run with specific level
./vendor/bin/phpstan analyse --level=5  # Use for stricter analysis
```

### Local Environment Commands

**Laravel Sail (Docker):**
```bash
# Install Sail
php artisan sail:install             # Use once during setup

# Sail commands
./vendor/bin/sail artisan migrate    # Use instead of php artisan when using Sail
./vendor/bin/sail composer install   # Use for Composer operations in Docker
./vendor/bin/sail npm run dev        # Use for frontend development in Docker
```

**Database Seeding:**
```bash
# Run all seeders
php artisan db:seed                  # Use for populating test data

# Run specific seeder
php artisan db:seed --class=UserSeeder  # Use for specific data sets

# Create new seeder
php artisan make:seeder ClientSeeder # Use when adding new test data
```

### Troubleshooting Common Issues

**"Class not found" errors:**
```bash
composer dump-autoload
php artisan config:clear
php artisan route:clear
```

**Frontend assets not updating:**
```bash
npm run build
php artisan view:clear
```

**Database connection issues:**
```bash
php artisan config:clear
# Check .env database credentials
# Verify MySQL service is running
```

**Livewire component not working:**
```bash
php artisan livewire:publish --config
php artisan view:clear
# Check component namespace and class name
```

---

## Project Structure & Conventions

### Folder Organization

**Livewire Components:**
```
app/
├── Livewire/
│   ├── Auth/           # Authentication components
│   ├── Dashboard/      # Dashboard widgets and components
│   ├── Forms/          # Reusable form components
│   ├── Tables/         # Data table components
│   └── Modals/         # Modal dialog components
```

**Filament Resources:**
```
app/
├── Filament/
│   ├── Resources/      # CRUD resources
│   ├── Pages/          # Custom admin pages
│   ├── Widgets/        # Dashboard widgets
│   └── Clusters/       # Resource groupings
```

**Custom Classes:**
```
app/
├── Services/           # Business logic services
├── Repositories/       # Data access layer
├── Traits/             # Reusable traits
├── Enums/              # Enumeration classes
└── DTOs/               # Data transfer objects
```

### Naming Conventions

**Models:** PascalCase, singular
```php
// ✅ Good
class Invoice extends Model {}
class InvoiceItem extends Model {}

// ❌ Bad
class invoices extends Model {}
class invoice_items extends Model {}
```

**Controllers:** PascalCase with "Controller" suffix
```php
// ✅ Good
class InvoiceController extends Controller {}
class Api\V1\InvoiceController extends Controller {}

// ❌ Bad
class invoiceController extends Controller {}
class InvoicesController extends Controller {}
```

**Database Tables:** snake_case, plural
```php
// ✅ Good
Schema::create('invoices', function (Blueprint $table) {});
Schema::create('invoice_items', function (Blueprint $table) {});

// ❌ Bad
Schema::create('Invoice', function (Blueprint $table) {});
Schema::create('invoiceItems', function (Blueprint $table) {});
```

**Livewire Components:** PascalCase
```php
// ✅ Good
class InvoiceForm extends Component {}
class UserTable extends Component {}

// ❌ Bad
class invoice_form extends Component {}
class userTable extends Component {}
```

### Code Style Guidelines

**Use Laravel Pint configuration:**
```json
{
    "preset": "laravel",
    "rules": {
        "simplified_null_return": true,
        "braces": {
            "position_after_control_structures": "same"
        }
    }
}
```

**Method Ordering in Controllers:**
```php
class InvoiceController extends Controller
{
    // 1. Resource methods in order
    public function index() {}
    public function create() {}
    public function store() {}
    public function show() {}
    public function edit() {}
    public function update() {}
    public function destroy() {}

    // 2. Custom methods
    public function export() {}
    public function duplicate() {}
}
```

**Eloquent Relationship Naming:**
```php
// ✅ Good - singular for belongsTo/hasOne
public function client() {
    return $this->belongsTo(Client::class);
}

// ✅ Good - plural for hasMany/belongsToMany
public function items() {
    return $this->hasMany(InvoiceItem::class);
}
```

---

## Quick Start Checklist

For experienced developers, follow this condensed setup:

1. **Create Project (5 min):**
   ```bash
   composer create-project laravel/laravel lial_erp_v1
   cd lial_erp_v1
   ```

2. **Install Stack (10 min):**
   ```bash
   composer require laravel/breeze --dev
   php artisan breeze:install livewire
   npm install && npm run build
   composer require filament/filament:"^3.2"
   php artisan filament:install --panels
   composer require spatie/laravel-permission
   ```

3. **Configure Database (5 min):**
   ```bash
   # Update .env with database credentials
   php artisan migrate
   php artisan make:filament-user
   ```

4. **Verify Installation:**
   ```bash
   php artisan serve
   # Visit /admin for Filament panel
   # Visit /login for Breeze authentication
   ```

---

## Next Steps

1. **Read Laravel Documentation:** [https://laravel.com/docs/11.x](https://laravel.com/docs/11.x)
2. **Livewire Documentation:** [https://livewire.laravel.com](https://livewire.laravel.com)
3. **Filament Documentation:** [https://filamentphp.com/docs](https://filamentphp.com/docs)
4. **Tailwind CSS Documentation:** [https://tailwindcss.com/docs](https://tailwindcss.com/docs)

## Glossary

- **Livewire:** Laravel package for building dynamic interfaces using PHP instead of JavaScript
- **Filament:** Admin panel builder for Laravel applications
- **Eloquent:** Laravel's built-in ORM (Object-Relational Mapping) system
- **Artisan:** Laravel's command-line interface for common tasks
- **Blade:** Laravel's templating engine for creating views
- **Migration:** Database version control system in Laravel
- **Seeder:** Class for populating database with test or initial data

